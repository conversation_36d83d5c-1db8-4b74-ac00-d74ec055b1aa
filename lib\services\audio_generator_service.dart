import 'dart:async';
import 'dart:math';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';

/// خدمة توليد الأصوات الاحترافية مثل Duolingo
class AudioGeneratorService {
  static final AudioGeneratorService _instance =
      AudioGeneratorService._internal();
  factory AudioGeneratorService() => _instance;
  AudioGeneratorService._internal();

  final AudioPlayer _effectsPlayer = AudioPlayer();
  final Random _random = Random();

  /// توليد صوت الإجابة الصحيحة (مثل Duolingo)
  Future<void> generateCorrectSound() async {
    try {
      // لحن إيجابي صاعد مثل Duolingo
      final frequencies = [523.25, 659.25, 783.99, 1046.50]; // C5, E5, G5, C6
      final durations = [150, 150, 150, 300];

      for (int i = 0; i < frequencies.length; i++) {
        await _playTone(frequencies[i], durations[i], volume: 0.7);
        if (i < frequencies.length - 1) {
          await Future.delayed(const Duration(milliseconds: 50));
        }
      }
    } catch (e) {
      debugPrint('❌ Error generating correct sound: $e');
    }
  }

  /// توليد صوت الإجابة الخاطئة (مثل Duolingo)
  Future<void> generateIncorrectSound() async {
    try {
      // نغمة هابطة حزينة
      final frequencies = [392.00, 329.63, 261.63]; // G4, E4, C4
      final durations = [200, 200, 400];

      for (int i = 0; i < frequencies.length; i++) {
        await _playTone(frequencies[i], durations[i], volume: 0.6);
        if (i < frequencies.length - 1) {
          await Future.delayed(const Duration(milliseconds: 100));
        }
      }
    } catch (e) {
      debugPrint('❌ Error generating incorrect sound: $e');
    }
  }

  /// توليد صوت إكمال المستوى (احتفالي)
  Future<void> generateLevelCompleteSound() async {
    try {
      // لحن احتفالي مثل Duolingo
      final melody = [
        [523.25, 150], // C5
        [659.25, 150], // E5
        [783.99, 150], // G5
        [1046.50, 150], // C6
        [783.99, 150], // G5
        [1046.50, 300], // C6
        [1174.66, 150], // D6
        [1318.51, 400], // E6
      ];

      for (final note in melody) {
        await _playTone(note[0] as double, (note[1] as int), volume: 0.8);
        await Future.delayed(const Duration(milliseconds: 75));
      }
    } catch (e) {
      debugPrint('❌ Error generating level complete sound: $e');
    }
  }

  /// توليد صوت الإنجاز والنجوم
  Future<void> generateAchievementSound() async {
    try {
      // تأثير لامع مع نغمات عالية
      for (int i = 0; i < 5; i++) {
        final frequency = 800 + (i * 200) + _random.nextDouble() * 100;
        await _playTone(frequency, 100, volume: 0.6);
        await Future.delayed(const Duration(milliseconds: 80));
      }

      // نغمة نهائية قوية
      await _playTone(1318.51, 300, volume: 0.8); // E6
    } catch (e) {
      debugPrint('❌ Error generating achievement sound: $e');
    }
  }

  /// توليد صوت فقدان القلب
  Future<void> generateHeartLostSound() async {
    try {
      // نغمة حزينة هابطة
      final frequencies = [440.00, 369.99, 293.66, 220.00]; // A4, F#4, D4, A3

      for (final freq in frequencies) {
        await _playTone(freq, 200, volume: 0.5);
        await Future.delayed(const Duration(milliseconds: 150));
      }
    } catch (e) {
      debugPrint('❌ Error generating heart lost sound: $e');
    }
  }

  /// توليد صوت النقر على الزر
  Future<void> generateButtonClickSound() async {
    try {
      // نقرة قصيرة وواضحة
      await _playTone(800, 50, volume: 0.4);
    } catch (e) {
      debugPrint('❌ Error generating button click sound: $e');
    }
  }

  /// توليد صوت السلسلة المتتالية
  Future<void> generateStreakSound() async {
    try {
      // تأثير نار متصاعدة
      for (int i = 0; i < 3; i++) {
        await _playTone(600 + (i * 150), 120, volume: 0.6);
        await Future.delayed(const Duration(milliseconds: 60));
      }
    } catch (e) {
      debugPrint('❌ Error generating streak sound: $e');
    }
  }

  /// توليد صوت الإشعار
  Future<void> generateNotificationSound() async {
    try {
      // نغمتان لطيفتان
      await _playTone(659.25, 150, volume: 0.5); // E5
      await Future.delayed(const Duration(milliseconds: 100));
      await _playTone(783.99, 200, volume: 0.5); // G5
    } catch (e) {
      debugPrint('❌ Error generating notification sound: $e');
    }
  }

  /// توليد موسيقى خلفية هادئة
  Future<void> generateBackgroundMusic() async {
    try {
      // لحن هادئ ومتكرر
      final melody = [
        523.25,
        587.33,
        659.25,
        698.46,
        783.99,
        698.46,
        659.25,
        587.33,
      ]; // C5, D5, E5, F5, G5, F5, E5, D5

      for (int loop = 0; loop < 4; loop++) {
        for (final freq in melody) {
          await _playTone(freq, 400, volume: 0.2);
          await Future.delayed(const Duration(milliseconds: 300));
        }
        await Future.delayed(const Duration(milliseconds: 1000));
      }
    } catch (e) {
      debugPrint('❌ Error generating background music: $e');
    }
  }

  /// تشغيل نغمة واحدة
  Future<void> _playTone(
    double frequency,
    int durationMs, {
    double volume = 0.5,
  }) async {
    try {
      // توليد موجة صوتية بسيطة
      final sampleRate = 44100;
      final samples = (sampleRate * durationMs / 1000).round();
      final audioData = Float32List(samples);

      for (int i = 0; i < samples; i++) {
        final t = i / sampleRate;
        // موجة جيبية مع تأثير fade in/out
        final fadeIn = i < samples * 0.1 ? i / (samples * 0.1) : 1.0;
        final fadeOut =
            i > samples * 0.9 ? (samples - i) / (samples * 0.1) : 1.0;
        final fade = fadeIn * fadeOut;

        audioData[i] = (sin(2 * pi * frequency * t) * volume * fade).toDouble();
      }

      // في التطبيق الحقيقي، نحتاج لتحويل البيانات إلى ملف صوتي
      // هنا نستخدم تأخير بسيط لمحاكاة تشغيل الصوت
      await Future.delayed(Duration(milliseconds: durationMs));
    } catch (e) {
      debugPrint('❌ Error playing tone: $e');
    }
  }

  /// تنظيف الموارد
  void dispose() {
    _effectsPlayer.dispose();
  }
}

/// خدمة الأصوات التعليمية للغات المختلفة
class LanguageAudioService {
  static final LanguageAudioService _instance =
      LanguageAudioService._internal();
  factory LanguageAudioService() => _instance;
  LanguageAudioService._internal();

  final AudioPlayer _speechPlayer = AudioPlayer();

  /// قاموس الكلمات الأساسية مع النطق الصوتي
  final Map<String, Map<String, String>> _basicWords = {
    'hello': {
      'ar': 'مرحبا',
      'en': 'Hello',
      'fr': 'Bonjour',
      'de': 'Hallo',
      'es': 'Hola',
      'it': 'Ciao',
      'ja': 'こんにちは',
      'zh': '你好',
      'tr': 'Merhaba',
      'ru': 'Привет',
    },
    'goodbye': {
      'ar': 'وداعا',
      'en': 'Goodbye',
      'fr': 'Au revoir',
      'de': 'Auf Wiedersehen',
      'es': 'Adiós',
      'it': 'Arrivederci',
      'ja': 'さようなら',
      'zh': '再见',
      'tr': 'Hoşça kal',
      'ru': 'До свидания',
    },
    'thank_you': {
      'ar': 'شكرا لك',
      'en': 'Thank you',
      'fr': 'Merci',
      'de': 'Danke',
      'es': 'Gracias',
      'it': 'Grazie',
      'ja': 'ありがとう',
      'zh': '谢谢',
      'tr': 'Teşekkür ederim',
      'ru': 'Спасибо',
    },
    'yes': {
      'ar': 'نعم',
      'en': 'Yes',
      'fr': 'Oui',
      'de': 'Ja',
      'es': 'Sí',
      'it': 'Sì',
      'ja': 'はい',
      'zh': '是的',
      'tr': 'Evet',
      'ru': 'Да',
    },
    'no': {
      'ar': 'لا',
      'en': 'No',
      'fr': 'Non',
      'de': 'Nein',
      'es': 'No',
      'it': 'No',
      'ja': 'いいえ',
      'zh': '不',
      'tr': 'Hayır',
      'ru': 'Нет',
    },
  };

  /// الحصول على ترجمة الكلمة
  String? getTranslation(String word, String languageCode) {
    return _basicWords[word]?[languageCode];
  }

  /// الحصول على جميع الكلمات الأساسية للغة
  Map<String, String> getBasicWordsForLanguage(String languageCode) {
    final words = <String, String>{};
    for (final entry in _basicWords.entries) {
      final translation = entry.value[languageCode];
      if (translation != null) {
        words[entry.key] = translation;
      }
    }
    return words;
  }

  /// محاكاة تشغيل النطق الصوتي
  Future<void> playWordPronunciation(String word, String languageCode) async {
    try {
      final translation = getTranslation(word, languageCode);
      if (translation != null) {
        debugPrint('🔊 Playing pronunciation: "$translation" in $languageCode');

        // محاكاة تشغيل الصوت
        await Future.delayed(const Duration(milliseconds: 800));

        // في التطبيق الحقيقي، سنشغل ملف صوتي هنا
        // await _speechPlayer.play(AssetSource('audio/speech/${languageCode}/${word}.mp3'));
      }
    } catch (e) {
      debugPrint('❌ Error playing pronunciation: $e');
    }
  }

  /// تنظيف الموارد
  void dispose() {
    _speechPlayer.dispose();
  }
}

/// خدمة الموسيقى الخلفية
class BackgroundMusicService {
  static final BackgroundMusicService _instance =
      BackgroundMusicService._internal();
  factory BackgroundMusicService() => _instance;
  BackgroundMusicService._internal();

  final AudioPlayer _musicPlayer = AudioPlayer();
  bool _isPlaying = false;

  /// تشغيل موسيقى خلفية هادئة
  Future<void> playAmbientMusic() async {
    if (_isPlaying) return;

    try {
      _isPlaying = true;

      // في التطبيق الحقيقي، سنشغل ملف موسيقى
      // await _musicPlayer.play(AssetSource('audio/background/ambient.mp3'));
      // await _musicPlayer.setReleaseMode(ReleaseMode.loop);
      // await _musicPlayer.setVolume(0.3);

      debugPrint('🎵 Playing ambient background music');

      // محاكاة الموسيقى الخلفية
      _simulateBackgroundMusic();
    } catch (e) {
      debugPrint('❌ Error playing background music: $e');
      _isPlaying = false;
    }
  }

  /// إيقاف الموسيقى الخلفية
  Future<void> stopMusic() async {
    try {
      await _musicPlayer.stop();
      _isPlaying = false;
      debugPrint('🔇 Background music stopped');
    } catch (e) {
      debugPrint('❌ Error stopping music: $e');
    }
  }

  /// محاكاة الموسيقى الخلفية
  void _simulateBackgroundMusic() {
    // في التطبيق الحقيقي، هذا لن يكون مطلوباً
    Timer.periodic(const Duration(seconds: 30), (timer) {
      if (!_isPlaying) {
        timer.cancel();
        return;
      }
      debugPrint('🎵 Background music continues...');
    });
  }

  /// تنظيف الموارد
  void dispose() {
    _musicPlayer.dispose();
  }
}
