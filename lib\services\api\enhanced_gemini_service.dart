import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../../config/constants.dart';

/// خدمة Gemini AI المحسنة للمحادثة الذكية
class EnhancedGeminiService {
  static const String _baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';
  
  // استخدام المفاتيح المحددة بالترتيب المطلوب
  static const List<String> _apiKeys = [
    AppConstants.keyGeminiApiKeyPaid,  // المفتاح المدفوع أولاً
    AppConstants.keyGeminiApiKeyFree,  // المفتاح المجاني كاحتياطي
  ];
  
  int _currentKeyIndex = 0;
  
  /// الحصول على مفتاح API النشط
  String get _currentApiKey => _apiKeys[_currentKeyIndex];
  
  /// التبديل إلى المفتاح التالي في حالة الفشل
  void _switchToNextKey() {
    if (_currentKeyIndex < _apiKeys.length - 1) {
      _currentKeyIndex++;
      debugPrint('🔄 تم التبديل إلى مفتاح API التالي: ${_currentKeyIndex + 1}');
    }
  }
  
  /// إرسال رسالة إلى Gemini مع سياق المحادثة
  Future<String> sendMessage({
    required String message,
    List<Map<String, String>>? chatHistory,
    String responseType = 'general',
  }) async {
    try {
      final prompt = _buildPrompt(message, responseType, chatHistory);
      
      for (int attempt = 0; attempt < _apiKeys.length; attempt++) {
        try {
          final response = await _makeRequest(prompt);
          if (response.isNotEmpty) {
            return response;
          }
        } catch (e) {
          debugPrint('❌ فشل في المحاولة ${attempt + 1}: $e');
          if (attempt < _apiKeys.length - 1) {
            _switchToNextKey();
          }
        }
      }
      
      return 'عذراً، حدث خطأ في الاتصال بالخدمة. يرجى المحاولة مرة أخرى لاحقاً.';
    } catch (e) {
      debugPrint('❌ خطأ عام في خدمة Gemini: $e');
      return 'عذراً، حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
    }
  }
  
  /// إجراء طلب HTTP إلى Gemini API
  Future<String> _makeRequest(String prompt) async {
    final url = Uri.parse('$_baseUrl?key=$_currentApiKey');
    
    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
      },
      body: json.encode({
        'contents': [
          {
            'parts': [
              {'text': prompt},
            ],
          },
        ],
        'generationConfig': {
          'temperature': 0.8,
          'topK': 40,
          'topP': 0.95,
          'maxOutputTokens': 2048,
          'stopSequences': [],
        },
        'safetySettings': [
          {
            'category': 'HARM_CATEGORY_HARASSMENT',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE',
          },
          {
            'category': 'HARM_CATEGORY_HATE_SPEECH',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE',
          },
          {
            'category': 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE',
          },
          {
            'category': 'HARM_CATEGORY_DANGEROUS_CONTENT',
            'threshold': 'BLOCK_MEDIUM_AND_ABOVE',
          },
        ],
      }),
    );
    
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      
      if (data['candidates'] != null && data['candidates'].isNotEmpty) {
        final content = data['candidates'][0]['content'];
        if (content != null && content['parts'] != null && content['parts'].isNotEmpty) {
          return content['parts'][0]['text']?.trim() ?? '';
        }
      }
      
      throw Exception('استجابة غير صالحة من الخدمة');
    } else if (response.statusCode == 429) {
      throw Exception('تم تجاوز الحد المسموح للطلبات');
    } else if (response.statusCode == 403) {
      throw Exception('مفتاح API غير صالح أو منتهي الصلاحية');
    } else {
      final errorData = json.decode(response.body);
      final errorMessage = errorData['error']?['message'] ?? 'خطأ غير معروف';
      throw Exception('خطأ من الخدمة: $errorMessage');
    }
  }
  
  /// بناء prompt محترف للذكاء الاصطناعي
  String _buildPrompt(String message, String responseType, List<Map<String, String>>? chatHistory) {
    final systemPrompt = _getSystemPrompt(responseType);
    
    String fullPrompt = systemPrompt;
    
    // إضافة تاريخ المحادثة إذا كان متوفراً
    if (chatHistory != null && chatHistory.isNotEmpty) {
      fullPrompt += '\n\n--- تاريخ المحادثة ---\n';
      for (final msg in chatHistory.take(10)) { // آخر 10 رسائل فقط
        final role = msg['role'] == 'user' ? 'المستخدم' : 'المساعد';
        fullPrompt += '$role: ${msg['content']}\n';
      }
      fullPrompt += '--- نهاية تاريخ المحادثة ---\n\n';
    }
    
    fullPrompt += 'المستخدم: $message\n\nالمساعد:';
    
    return fullPrompt;
  }
  
  /// الحصول على prompt النظام حسب نوع الاستجابة
  String _getSystemPrompt(String responseType) {
    switch (responseType) {
      case 'translation':
        return '''أنت مساعد ترجمة ذكي ومتخصص. مهمتك:
1. ترجمة النصوص بدقة عالية بين اللغات المختلفة
2. شرح الفروق الثقافية والسياقية في الترجمة
3. تقديم ترجمات بديلة عند الحاجة
4. توضيح معاني الكلمات والتعبيرات الصعبة
5. مساعدة المستخدم في فهم القواعد النحوية

تحدث بالعربية بطلاقة وكن مفيداً ومهذباً.''';

      case 'language_learning':
        return '''أنت معلم لغات خبير ومتحمس. مهمتك:
1. تعليم اللغات بطريقة تفاعلية وممتعة
2. تصحيح الأخطاء النحوية والإملائية بلطف
3. تقديم تمارين وأمثلة عملية
4. شرح القواعد بطريقة مبسطة ومفهومة
5. تشجيع المتعلم وتحفيزه على التقدم
6. تقديم نصائح لتحسين النطق والمحادثة

كن صبوراً ومشجعاً واستخدم أمثلة من الحياة اليومية.''';

      case 'grammar_help':
        return '''أنت خبير في قواعد اللغة العربية والإنجليزية. مهمتك:
1. شرح القواعد النحوية والصرفية بوضوح
2. تصحيح الأخطاء وتوضيح السبب
3. تقديم أمثلة متنوعة لكل قاعدة
4. مقارنة القواعد بين اللغات المختلفة
5. تبسيط المفاهيم المعقدة

استخدم أسلوباً تعليمياً واضحاً ومنظماً.''';

      case 'conversation_practice':
        return '''أنت شريك محادثة ودود ومتفهم. مهمتك:
1. إجراء محادثات طبيعية وممتعة
2. تصحيح الأخطاء بلطف دون مقاطعة التدفق
3. طرح أسئلة لتشجيع المحادثة
4. استخدام مفردات متنوعة ومناسبة للمستوى
5. تقديم تعبيرات وعبارات جديدة

كن طبيعياً وودوداً واجعل المحادثة ممتعة.''';

      default: // general
        return '''أنت مساعد ذكي متخصص في الترجمة وتعلم اللغات. أنت:
1. خبير في الترجمة بين العربية والإنجليزية واللغات الأخرى
2. معلم لغات صبور ومتفهم
3. مساعد في تعلم القواعد والمفردات
4. مرشد للثقافات واللهجات المختلفة
5. مصحح للأخطاء اللغوية والنحوية

مهمتك مساعدة المستخدم في:
- الترجمة الدقيقة والسياقية
- تعلم اللغات وتحسين المهارات
- فهم الثقافات والتعبيرات
- تطوير مهارات المحادثة والكتابة

تحدث بالعربية بطلاقة، كن مفيداً ومهذباً ومشجعاً. قدم إجابات شاملة ومفصلة.''';
    }
  }
  
  /// إعادة تعيين مؤشر المفتاح
  void resetKeyIndex() {
    _currentKeyIndex = 0;
  }
  
  /// الحصول على حالة المفتاح الحالي
  String getCurrentKeyStatus() {
    return 'المفتاح النشط: ${_currentKeyIndex + 1}/${_apiKeys.length}';
  }
}
