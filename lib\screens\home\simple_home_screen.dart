import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../config/app_theme.dart';
import '../../utils/responsive_helper.dart';
import '../translation/translation_screen.dart';
import '../settings/modern_settings_screen.dart';
import '../tourism/tourism_screen.dart';
import '../children/children_education_screen.dart';
import '../duolingo/professional_duolingo_screen.dart';
import '../../widgets/ai_chat_floating_button.dart';

/// شاشة رئيسية مبسطة لا تحتاج Provider
class SimpleHomeScreen extends StatefulWidget {
  const SimpleHomeScreen({super.key});

  @override
  State<SimpleHomeScreen> createState() => _SimpleHomeScreenState();
}

class _SimpleHomeScreenState extends State<SimpleHomeScreen> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: ResponsiveText(
          'المترجم الذكي AI',
          mobileFontSize: 18,
          tabletFontSize: 20,
          desktopFontSize: 22,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        centerTitle: true,
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        toolbarHeight: ResponsiveHelper.getAppBarHeight(context),
        actions: [
          IconButton(
            icon: Icon(
              Icons.settings,
              size: ResponsiveHelper.getResponsiveIconSize(context),
            ),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ModernSettingsScreen(),
                ),
              );
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          _buildBody(),
          // زر الدردشة العائم
          const AIChatFloatingButton(),
        ],
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) {
            HapticFeedback.lightImpact();
            setState(() {
              _currentIndex = index;
            });
          },
          type: BottomNavigationBarType.fixed,
          selectedItemColor: AppTheme.primaryColor,
          unselectedItemColor: Colors.grey,
          backgroundColor: Colors.transparent,
          elevation: 0,
          selectedLabelStyle: const TextStyle(fontWeight: FontWeight.bold),
          items: const [
            BottomNavigationBarItem(
              icon: Icon(Icons.home_rounded),
              activeIcon: Icon(Icons.home),
              label: 'الرئيسية',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.favorite_border_rounded),
              activeIcon: Icon(Icons.favorite),
              label: 'المفضلة',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.history_rounded),
              activeIcon: Icon(Icons.history),
              label: 'السجل',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.settings_outlined),
              activeIcon: Icon(Icons.settings),
              label: 'الإعدادات',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBody() {
    switch (_currentIndex) {
      case 0:
        return _buildHomeTab();
      case 1:
        return _buildFavoritesTab();
      case 2:
        return _buildHistoryTab();
      case 3:
        return _buildSettingsTab();
      default:
        return _buildHomeTab();
    }
  }

  Widget _buildHomeTab() {
    return ResponsiveLayout(
      child: Padding(
        padding: EdgeInsets.all(
          ResponsiveHelper.getResponsivePadding(
            context,
            mobile: 16,
            tablet: 24,
            desktop: 32,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رسالة ترحيب
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(
                ResponsiveHelper.getResponsivePadding(
                  context,
                  mobile: 16,
                  tablet: 20,
                  desktop: 24,
                ),
              ),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(
                  ResponsiveHelper.getResponsiveBorderRadius(context),
                ),
                border: Border.all(
                  color: AppTheme.primaryColor.withValues(alpha: 0.3),
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.translate,
                    color: AppTheme.primaryColor,
                    size: ResponsiveHelper.getResponsiveIconSize(
                      context,
                      mobile: 32,
                      tablet: 36,
                      desktop: 40,
                    ),
                  ),
                  SizedBox(
                    height: ResponsiveHelper.getResponsivePadding(
                      context,
                      mobile: 8,
                      tablet: 10,
                      desktop: 12,
                    ),
                  ),
                  ResponsiveText(
                    'مرحباً بك في المترجم الذكي AI',
                    mobileFontSize: 16,
                    tabletFontSize: 18,
                    desktopFontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(
                    height: ResponsiveHelper.getResponsivePadding(
                      context,
                      mobile: 4,
                      tablet: 6,
                      desktop: 8,
                    ),
                  ),
                  ResponsiveText(
                    'اختر الميزة التي تريد استخدامها',
                    mobileFontSize: 12,
                    tabletFontSize: 14,
                    desktopFontSize: 16,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            SizedBox(
              height: ResponsiveHelper.getResponsivePadding(
                context,
                mobile: 20,
                tablet: 28,
                desktop: 36,
              ),
            ),

            ResponsiveText(
              'اختر نوع الترجمة',
              mobileFontSize: 18,
              tabletFontSize: 22,
              desktopFontSize: 26,
              fontWeight: FontWeight.bold,
            ),

            SizedBox(
              height: ResponsiveHelper.getResponsivePadding(
                context,
                mobile: 16,
                tablet: 20,
                desktop: 24,
              ),
            ),

            Expanded(
              child: LayoutBuilder(
                builder: (context, constraints) {
                  final crossAxisCount = ResponsiveHelper.getGridColumns(
                    context,
                    mobile: 2,
                    tablet: 3,
                    desktop: 4,
                  );

                  final spacing = ResponsiveHelper.getResponsivePadding(
                    context,
                    mobile: 12,
                    tablet: 16,
                    desktop: 20,
                  );

                  return GridView.count(
                    crossAxisCount: crossAxisCount,
                    crossAxisSpacing: spacing,
                    mainAxisSpacing: spacing,
                    childAspectRatio: ResponsiveHelper.getCardAspectRatio(
                      context,
                    ),
                    children: [
                      _buildFeatureCard(
                        context,
                        'ترجمة نصوص',
                        Icons.text_fields,
                        () => _navigateToTranslation('text'),
                      ),
                      _buildFeatureCard(
                        context,
                        'ترجمة صوتية',
                        Icons.mic,
                        () => _navigateToTranslation('voice'),
                      ),
                      _buildFeatureCard(
                        context,
                        'ترجمة صور',
                        Icons.camera_alt,
                        () => _navigateToTranslation('image'),
                      ),
                      _buildFeatureCard(
                        context,
                        'ترجمة مستندات',
                        Icons.description,
                        () => _navigateToTranslation('document'),
                      ),
                      _buildFeatureCard(
                        context,
                        'محادثة ذكية',
                        Icons.smart_toy,
                        () => _navigateToTranslation('ai_chat'),
                      ),
                      _buildFeatureCard(
                        context,
                        'عبارات السفر',
                        Icons.flight_takeoff,
                        () => _navigateToTourism(),
                      ),
                      _buildFeatureCard(
                        context,
                        'تعليم الأطفال',
                        Icons.child_care,
                        () => _navigateToChildrenEducation(),
                      ),
                      _buildFeatureCard(
                        context,
                        'تعلم اللغات',
                        Icons.school,
                        () => _navigateToDuolingo(),
                      ),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureCard(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    final borderRadius = ResponsiveHelper.getResponsiveBorderRadius(context);
    final padding = ResponsiveHelper.getResponsivePadding(
      context,
      mobile: 16,
      tablet: 20,
      desktop: 24,
    );
    final iconSize = ResponsiveHelper.getResponsiveIconSize(
      context,
      mobile: 28,
      tablet: 32,
      desktop: 36,
    );

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.primaryColor.withValues(alpha: 0.1),
            AppTheme.primaryColor.withValues(alpha: 0.05),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: ResponsiveHelper.isSmallScreen(context) ? 6 : 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            // إضافة haptic feedback
            HapticFeedback.lightImpact();
            onTap();
          },
          borderRadius: BorderRadius.circular(borderRadius),
          child: Padding(
            padding: EdgeInsets.all(padding),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: EdgeInsets.all(padding * 0.75),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.15),
                    borderRadius: BorderRadius.circular(borderRadius * 0.75),
                  ),
                  child: Icon(
                    icon,
                    size: iconSize,
                    color: AppTheme.primaryColor,
                  ),
                ),
                SizedBox(height: padding * 0.75),
                ResponsiveText(
                  title,
                  mobileFontSize: 12,
                  tabletFontSize: 14,
                  desktopFontSize: 16,
                  fontWeight: FontWeight.bold,
                  textAlign: TextAlign.center,
                  maxLines: 2,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFavoritesTab() {
    return ResponsiveLayout(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.favorite,
              size: ResponsiveHelper.getResponsiveIconSize(
                context,
                mobile: 48,
                tablet: 56,
                desktop: 64,
              ),
              color: Colors.grey,
            ),
            SizedBox(
              height: ResponsiveHelper.getResponsivePadding(
                context,
                mobile: 12,
                tablet: 16,
                desktop: 20,
              ),
            ),
            ResponsiveText(
              'لا توجد عناصر مفضلة حتى الآن',
              mobileFontSize: 16,
              tabletFontSize: 18,
              desktopFontSize: 20,
              fontWeight: FontWeight.w500,
              textAlign: TextAlign.center,
            ),
            SizedBox(
              height: ResponsiveHelper.getResponsivePadding(
                context,
                mobile: 6,
                tablet: 8,
                desktop: 10,
              ),
            ),
            ResponsiveText(
              'ستظهر هنا العناصر التي تضيفها إلى المفضلة',
              mobileFontSize: 12,
              tabletFontSize: 14,
              desktopFontSize: 16,
              color: Colors.grey,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHistoryTab() {
    return ResponsiveLayout(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history,
              size: ResponsiveHelper.getResponsiveIconSize(
                context,
                mobile: 48,
                tablet: 56,
                desktop: 64,
              ),
              color: Colors.grey,
            ),
            SizedBox(
              height: ResponsiveHelper.getResponsivePadding(
                context,
                mobile: 12,
                tablet: 16,
                desktop: 20,
              ),
            ),
            ResponsiveText(
              'لا يوجد سجل حتى الآن',
              mobileFontSize: 16,
              tabletFontSize: 18,
              desktopFontSize: 20,
              fontWeight: FontWeight.w500,
              textAlign: TextAlign.center,
            ),
            SizedBox(
              height: ResponsiveHelper.getResponsivePadding(
                context,
                mobile: 6,
                tablet: 8,
                desktop: 10,
              ),
            ),
            ResponsiveText(
              'سيظهر هنا سجل الترجمات السابقة',
              mobileFontSize: 12,
              tabletFontSize: 14,
              desktopFontSize: 16,
              color: Colors.grey,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsTab() {
    return ResponsiveLayout(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.settings,
              size: ResponsiveHelper.getResponsiveIconSize(
                context,
                mobile: 48,
                tablet: 56,
                desktop: 64,
              ),
              color: Colors.grey,
            ),
            SizedBox(
              height: ResponsiveHelper.getResponsivePadding(
                context,
                mobile: 12,
                tablet: 16,
                desktop: 20,
              ),
            ),
            ResponsiveText(
              'الإعدادات',
              mobileFontSize: 16,
              tabletFontSize: 18,
              desktopFontSize: 20,
              fontWeight: FontWeight.w500,
              textAlign: TextAlign.center,
            ),
            SizedBox(
              height: ResponsiveHelper.getResponsivePadding(
                context,
                mobile: 6,
                tablet: 8,
                desktop: 10,
              ),
            ),
            ResponsiveText(
              'قريباً ستتوفر إعدادات التطبيق',
              mobileFontSize: 12,
              tabletFontSize: 14,
              desktopFontSize: 16,
              color: Colors.grey,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToTranslation(String type) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TranslationScreen(translationType: type),
      ),
    );
  }

  void _navigateToTourism() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const TourismScreen()),
    );
  }

  void _navigateToChildrenEducation() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const ChildrenEducationScreen()),
    );
  }

  void _navigateToDuolingo() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ProfessionalDuolingoScreen(),
      ),
    );
  }
}
