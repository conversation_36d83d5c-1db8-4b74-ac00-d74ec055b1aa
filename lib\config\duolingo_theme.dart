import 'package:flutter/material.dart';

/// ثيم Duolingo الحقيقي والاحترافي
class DuolingoTheme {
  // الألوان الأساسية لـ Duolingo
  static const Color primaryGreen = Color(0xFF58CC02);
  static const Color darkGreen = Color(0xFF58A700);
  static const Color lightGreen = Color(0xFF89E219);
  static const Color superLightGreen = Color(0xFFD7FFB8);

  static const Color primaryBlue = Color(0xFF1CB0F6);
  static const Color darkBlue = Color(0xFF1899D6);
  static const Color lightBlue = Color(0xFF84D8FF);

  static const Color primaryOrange = Color(0xFFFF9600);
  static const Color darkOrange = Color(0xFFFF8500);
  static const Color lightOrange = Color(0xFFFFB800);

  static const Color primaryRed = Color(0xFFFF4B4B);
  static const Color darkRed = Color(0xFFEA2B2B);
  static const Color lightRed = Color(0xFFFFCCCC);

  static const Color primaryPurple = Color(0xFFCE82FF);
  static const Color darkPurple = Color(0xFFB566D9);

  static const Color primaryPink = Color(0xFFFF86D0);
  static const Color darkPink = Color(0xFFE671B8);

  // ألوان النصوص
  static const Color textPrimary = Color(0xFF3C3C3C);
  static const Color textSecondary = Color(0xFF777777);
  static const Color textLight = Color(0xFFAFAFAF);
  static const Color textWhite = Color(0xFFFFFFFF);

  // ألوان الخلفية
  static const Color backgroundPrimary = Color(0xFFF7F8FA);
  static const Color backgroundSecondary = Color(0xFFFFFFFF);
  static const Color backgroundCard = Color(0xFFFFFFFF);

  // ألوان الحدود
  static const Color borderLight = Color(0xFFE5E5E5);
  static const Color borderMedium = Color(0xFFCECECE);
  static const Color borderDark = Color(0xFFADADAD);

  // ألوان الحالات
  static const Color success = primaryGreen;
  static const Color error = primaryRed;
  static const Color warning = primaryOrange;
  static const Color info = primaryBlue;

  // الظلال
  static List<BoxShadow> get cardShadow => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.1),
      offset: const Offset(0, 2),
      blurRadius: 8,
      spreadRadius: 0,
    ),
  ];

  static List<BoxShadow> get buttonShadow => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.15),
      offset: const Offset(0, 4),
      blurRadius: 8,
      spreadRadius: 0,
    ),
  ];

  static List<BoxShadow> get elevatedShadow => [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.2),
      offset: const Offset(0, 8),
      blurRadius: 16,
      spreadRadius: 0,
    ),
  ];

  // التدرجات اللونية
  static LinearGradient get primaryGradient => const LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryGreen, lightGreen],
  );

  static LinearGradient get blueGradient => const LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryBlue, lightBlue],
  );

  static LinearGradient get orangeGradient => const LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryOrange, lightOrange],
  );

  static LinearGradient get redGradient => const LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryRed, lightRed],
  );

  // أنماط النصوص
  static const TextStyle headingLarge = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.w800,
    color: textPrimary,
    fontFamily: 'Cairo',
    height: 1.2,
  );

  static const TextStyle headingMedium = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w700,
    color: textPrimary,
    fontFamily: 'Cairo',
    height: 1.3,
  );

  static const TextStyle headingSmall = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    color: textPrimary,
    fontFamily: 'Cairo',
    height: 1.4,
  );

  static const TextStyle bodyLarge = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w500,
    color: textPrimary,
    fontFamily: 'Cairo',
    height: 1.5,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: textPrimary,
    fontFamily: 'Cairo',
    height: 1.5,
  );

  static const TextStyle bodySmall = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: textSecondary,
    fontFamily: 'Cairo',
    height: 1.4,
  );

  static const TextStyle caption = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    color: textLight,
    fontFamily: 'Cairo',
    height: 1.3,
  );

  static const TextStyle buttonText = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w700,
    color: textWhite,
    fontFamily: 'Cairo',
    height: 1.2,
  );

  // أنماط الأزرار
  static ButtonStyle get primaryButtonStyle => ElevatedButton.styleFrom(
    backgroundColor: primaryGreen,
    foregroundColor: textWhite,
    elevation: 0,
    shadowColor: Colors.transparent,
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
    textStyle: buttonText,
  );

  static ButtonStyle get secondaryButtonStyle => ElevatedButton.styleFrom(
    backgroundColor: backgroundSecondary,
    foregroundColor: textPrimary,
    elevation: 0,
    shadowColor: Colors.transparent,
    side: const BorderSide(color: borderMedium, width: 2),
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
    textStyle: buttonText.copyWith(color: textPrimary),
  );

  static ButtonStyle get dangerButtonStyle => ElevatedButton.styleFrom(
    backgroundColor: primaryRed,
    foregroundColor: textWhite,
    elevation: 0,
    shadowColor: Colors.transparent,
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
    textStyle: buttonText,
  );

  // أنماط البطاقات
  static BoxDecoration get cardDecoration => BoxDecoration(
    color: backgroundCard,
    borderRadius: BorderRadius.circular(16),
    border: Border.all(color: borderLight, width: 2),
    boxShadow: cardShadow,
  );

  static BoxDecoration get elevatedCardDecoration => BoxDecoration(
    color: backgroundCard,
    borderRadius: BorderRadius.circular(20),
    border: Border.all(color: borderLight, width: 2),
    boxShadow: elevatedShadow,
  );

  // أنماط حقول الإدخال
  static InputDecoration getInputDecoration({
    required String hintText,
    String? labelText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    bool isError = false,
  }) => InputDecoration(
    hintText: hintText,
    labelText: labelText,
    prefixIcon: prefixIcon,
    suffixIcon: suffixIcon,
    filled: true,
    fillColor: backgroundSecondary,
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(16),
      borderSide: BorderSide(color: isError ? error : borderMedium, width: 2),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(16),
      borderSide: BorderSide(color: isError ? error : borderLight, width: 2),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(16),
      borderSide: BorderSide(color: isError ? error : primaryBlue, width: 2),
    ),
    errorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(16),
      borderSide: const BorderSide(color: error, width: 2),
    ),
    contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
    hintStyle: bodyMedium.copyWith(color: textLight),
    labelStyle: bodyMedium.copyWith(color: textSecondary),
  );

  // الثيم الكامل
  static ThemeData get theme => ThemeData(
    useMaterial3: true,
    fontFamily: 'Cairo',
    colorScheme: ColorScheme.fromSeed(
      seedColor: primaryGreen,
      brightness: Brightness.light,
    ),
    scaffoldBackgroundColor: backgroundPrimary,
    appBarTheme: const AppBarTheme(
      backgroundColor: primaryGreen,
      foregroundColor: textWhite,
      elevation: 0,
      centerTitle: true,
      titleTextStyle: headingMedium,
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(style: primaryButtonStyle),
    cardTheme: CardTheme(
      color: backgroundCard,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: const BorderSide(color: borderLight, width: 2),
      ),
      margin: const EdgeInsets.all(8),
    ),
    textTheme: const TextTheme(
      headlineLarge: headingLarge,
      headlineMedium: headingMedium,
      headlineSmall: headingSmall,
      bodyLarge: bodyLarge,
      bodyMedium: bodyMedium,
      bodySmall: bodySmall,
      labelLarge: buttonText,
    ),
  );
}
