import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../../config/duolingo_theme.dart';
import '../../services/api/enhanced_gemini_service.dart';

/// نموذج رسالة المحادثة
class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;
  final String? messageId;

  ChatMessage({
    required this.text,
    required this.isUser,
    DateTime? timestamp,
    this.messageId,
  }) : timestamp = timestamp ?? DateTime.now();

  /// تحويل إلى Map للحفظ
  Map<String, dynamic> toMap() {
    return {
      'text': text,
      'isUser': isUser,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'messageId': messageId,
    };
  }

  /// إنشاء من Map
  factory ChatMessage.fromMap(Map<String, dynamic> map) {
    return ChatMessage(
      text: map['text'] ?? '',
      isUser: map['isUser'] ?? false,
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp'] ?? 0),
      messageId: map['messageId'],
    );
  }
}

/// شاشة المحادثة الذكية الاحترافية
class AIChatScreen extends StatefulWidget {
  const AIChatScreen({super.key});

  @override
  State<AIChatScreen> createState() => _AIChatScreenState();
}

class _AIChatScreenState extends State<AIChatScreen>
    with TickerProviderStateMixin {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<ChatMessage> _messages = [];
  final FocusNode _textFieldFocus = FocusNode();

  bool _isTyping = false;
  String _selectedResponseType = 'general';
  late EnhancedGeminiService _geminiService;
  late AnimationController _typingAnimationController;
  late Animation<double> _typingAnimation;

  // مفاتيح للحفظ المحلي
  static const String _chatHistoryKey = 'ai_chat_history';
  static const String _responseTypeKey = 'ai_response_type';

  @override
  void initState() {
    super.initState();

    _geminiService = EnhancedGeminiService();

    // تهيئة الانيميشن
    _typingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _typingAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _typingAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    _loadChatHistory();
    _loadResponseType();

    // إضافة رسالة ترحيب إذا كانت المحادثة فارغة
    if (_messages.isEmpty) {
      _addBotMessage(
        'مرحباً بك في المساعد الذكي للترجمة! 🤖\n\nأنا هنا لمساعدتك في:\n• الترجمة بين اللغات المختلفة\n• تعلم اللغات وتحسين المهارات\n• شرح القواعد النحوية\n• ممارسة المحادثة\n\nكيف يمكنني مساعدتك اليوم؟',
      );
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _textFieldFocus.dispose();
    _typingAnimationController.dispose();
    super.dispose();
  }

  /// تحميل تاريخ المحادثة من التخزين المحلي
  Future<void> _loadChatHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final chatHistoryJson = prefs.getString(_chatHistoryKey);

      if (chatHistoryJson != null) {
        final List<dynamic> chatList = json.decode(chatHistoryJson);
        setState(() {
          _messages.clear();
          _messages.addAll(
            chatList.map((item) => ChatMessage.fromMap(item)).toList(),
          );
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل تاريخ المحادثة: $e');
    }
  }

  /// حفظ تاريخ المحادثة في التخزين المحلي
  Future<void> _saveChatHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final chatHistoryJson = json.encode(
        _messages.map((message) => message.toMap()).toList(),
      );
      await prefs.setString(_chatHistoryKey, chatHistoryJson);
    } catch (e) {
      debugPrint('خطأ في حفظ تاريخ المحادثة: $e');
    }
  }

  /// تحميل نوع الاستجابة المحفوظ
  Future<void> _loadResponseType() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedType = prefs.getString(_responseTypeKey);
      if (savedType != null) {
        setState(() {
          _selectedResponseType = savedType;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل نوع الاستجابة: $e');
    }
  }

  /// حفظ نوع الاستجابة
  Future<void> _saveResponseType() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_responseTypeKey, _selectedResponseType);
    } catch (e) {
      debugPrint('خطأ في حفظ نوع الاستجابة: $e');
    }
  }

  /// إضافة رسالة من المستخدم
  void _addUserMessage(String message) {
    final userMessage = ChatMessage(
      text: message,
      isUser: true,
      messageId: DateTime.now().millisecondsSinceEpoch.toString(),
    );

    setState(() {
      _messages.add(userMessage);
    });

    _scrollToBottom();
    _saveChatHistory();
  }

  /// إضافة رسالة من الروبوت
  void _addBotMessage(String message) {
    final botMessage = ChatMessage(
      text: message,
      isUser: false,
      messageId: DateTime.now().millisecondsSinceEpoch.toString(),
    );

    setState(() {
      _messages.add(botMessage);
    });

    _scrollToBottom();
    _saveChatHistory();
  }

  /// التمرير إلى أسفل المحادثة
  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  /// إرسال رسالة
  Future<void> _sendMessage() async {
    final message = _messageController.text.trim();

    if (message.isEmpty || _isTyping) return;

    // إضافة haptic feedback
    HapticFeedback.lightImpact();

    _messageController.clear();
    _addUserMessage(message);

    setState(() {
      _isTyping = true;
    });

    // بدء انيميشن الكتابة
    _typingAnimationController.repeat();

    try {
      // التحقق من الحد الأقصى للرسائل المجانية (مبسط)
      if (_messages.length > 50) {
        _addBotMessage(
          'لقد وصلت إلى الحد الأقصى للرسائل في هذه الجلسة. يمكنك مسح المحادثة والبدء من جديد.',
        );
        return;
      }

      // تحضير تاريخ المحادثة للإرسال
      final chatHistory =
          _messages
              .where((m) => m.text.isNotEmpty)
              .take(20) // آخر 20 رسالة فقط
              .map(
                (m) => {
                  'role': m.isUser ? 'user' : 'assistant',
                  'content': m.text,
                },
              )
              .toList();

      // الحصول على رد من Gemini
      final response = await _geminiService.sendMessage(
        message: message,
        chatHistory: chatHistory,
        responseType: _selectedResponseType,
      );

      if (mounted && response.isNotEmpty) {
        _addBotMessage(response);
      } else if (mounted) {
        _addBotMessage(
          'عذراً، لم أتمكن من الحصول على رد. يرجى المحاولة مرة أخرى.',
        );
      }
    } catch (e) {
      debugPrint('خطأ في إرسال الرسالة: $e');
      if (mounted) {
        _addBotMessage(
          'عذراً، حدث خطأ أثناء معالجة طلبك. يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.',
        );

        // إظهار رسالة خطأ مفصلة للمطور
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: ${e.toString()}'),
            backgroundColor: DuolingoTheme.primaryRed,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isTyping = false;
        });
        _typingAnimationController.stop();
        _typingAnimationController.reset();
      }
    }
  }

  /// مسح المحادثة
  Future<void> _clearChat() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('مسح المحادثة'),
            content: const Text('هل أنت متأكد من أنك تريد مسح جميع الرسائل؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(
                  foregroundColor: DuolingoTheme.primaryRed,
                ),
                child: const Text('مسح'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      setState(() {
        _messages.clear();
      });
      await _saveChatHistory();

      // إضافة رسالة ترحيب جديدة
      _addBotMessage('تم مسح المحادثة. كيف يمكنني مساعدتك اليوم؟');

      HapticFeedback.mediumImpact();
    }
  }

  /// تغيير نوع الاستجابة
  void _changeResponseType(String type) {
    setState(() {
      _selectedResponseType = type;
    });

    _saveResponseType();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تم تغيير نوع الاستجابة إلى: ${_getResponseTypeName(type)}',
        ),
        backgroundColor: DuolingoTheme.primaryGreen,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// الحصول على اسم نوع الاستجابة
  String _getResponseTypeName(String type) {
    switch (type) {
      case 'translation':
        return 'الترجمة';
      case 'language_learning':
        return 'تعلم اللغات';
      case 'grammar_help':
        return 'مساعدة القواعد';
      case 'conversation_practice':
        return 'ممارسة المحادثة';
      default:
        return 'عام';
    }
  }

  /// أنواع الاستجابة المتاحة
  List<Map<String, String>> get _responseTypes => [
    {'id': 'general', 'title': 'عام', 'icon': '💬'},
    {'id': 'translation', 'title': 'الترجمة', 'icon': '🔄'},
    {'id': 'language_learning', 'title': 'تعلم اللغات', 'icon': '📚'},
    {'id': 'grammar_help', 'title': 'مساعدة القواعد', 'icon': '📝'},
    {'id': 'conversation_practice', 'title': 'ممارسة المحادثة', 'icon': '🗣️'},
  ];

  /// عرض مربع حوار اختيار نوع الاستجابة
  void _showResponseTypeSelector() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // مقبض السحب
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 16),

                // العنوان
                Row(
                  children: [
                    Icon(Icons.tune, color: DuolingoTheme.primaryBlue),
                    const SizedBox(width: 8),
                    const Text(
                      'اختر نوع المساعدة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // قائمة الخيارات
                ...(_responseTypes.map((type) {
                  final id = type['id']!;
                  final title = type['title']!;
                  final icon = type['icon']!;
                  final isSelected = _selectedResponseType == id;

                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      leading: Text(icon, style: const TextStyle(fontSize: 24)),
                      title: Text(title),
                      trailing:
                          isSelected
                              ? Icon(
                                Icons.check_circle,
                                color: DuolingoTheme.primaryGreen,
                              )
                              : const Icon(
                                Icons.radio_button_unchecked,
                                color: Colors.grey,
                              ),
                      selected: isSelected,
                      selectedTileColor: DuolingoTheme.primaryGreen.withValues(
                        alpha: 0.1,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      onTap: () {
                        Navigator.pop(context);
                        _changeResponseType(id);
                        HapticFeedback.selectionClick();
                      },
                    ),
                  );
                }).toList()),

                const SizedBox(height: 10),
              ],
            ),
          ),
    );
  }

  /// تنسيق التاريخ
  String _formatTimestamp(DateTime timestamp) {
    return DateFormat('HH:mm').format(timestamp);
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: DuolingoTheme.backgroundPrimary,
        appBar: AppBar(
          title: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.smart_toy, color: Colors.white),
              const SizedBox(width: 8),
              const Text(
                'المساعد الذكي',
                style: TextStyle(color: Colors.white),
              ),
            ],
          ),
          centerTitle: true,
          backgroundColor: DuolingoTheme.primaryGreen,
          elevation: 0,
          actions: [
            // زر مسح المحادثة
            IconButton(
              icon: const Icon(Icons.delete_outline, color: Colors.white),
              onPressed: _clearChat,
              tooltip: 'مسح المحادثة',
            ),
            // زر اختيار نوع الاستجابة
            IconButton(
              icon: const Icon(Icons.tune, color: Colors.white),
              onPressed: _showResponseTypeSelector,
              tooltip: 'نوع المساعدة',
            ),
          ],
        ),
        body: Column(
          children: [
            // شريط نوع الاستجابة
            Container(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                color: DuolingoTheme.backgroundCard,
                border: Border(
                  bottom: BorderSide(
                    color: DuolingoTheme.borderLight,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.psychology,
                    color: DuolingoTheme.primaryBlue,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'نوع المساعدة:',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: DuolingoTheme.textPrimary,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: DuolingoTheme.primaryBlue.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: DuolingoTheme.primaryBlue.withValues(
                            alpha: 0.3,
                          ),
                        ),
                      ),
                      child: Text(
                        _getResponseTypeName(_selectedResponseType),
                        style: TextStyle(
                          color: DuolingoTheme.primaryBlue,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // قائمة الرسائل
            Expanded(
              child:
                  _messages.isEmpty
                      ? _buildEmptyState()
                      : ListView.builder(
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16),
                        itemCount: _messages.length,
                        itemBuilder: (context, index) {
                          final message = _messages[index];
                          return _buildMessageItem(message);
                        },
                      ),
            ),

            // مؤشر الكتابة مع انيميشن
            if (_isTyping) _buildTypingIndicator(),

            // حقل إدخال الرسالة
            _buildMessageInput(),
          ],
        ),
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: DuolingoTheme.primaryGreen.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.smart_toy,
              size: 60,
              color: DuolingoTheme.primaryGreen,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'مرحباً بك في المساعد الذكي! 👋',
            style: DuolingoTheme.headingMedium.copyWith(
              color: DuolingoTheme.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              'أنا هنا لمساعدتك في الترجمة وتعلم اللغات.\nابدأ بكتابة رسالة أدناه!',
              style: DuolingoTheme.bodyMedium.copyWith(
                color: DuolingoTheme.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء مؤشر الكتابة
  Widget _buildTypingIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 20),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: DuolingoTheme.primaryGreen.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.smart_toy,
              color: DuolingoTheme.primaryGreen,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'يكتب',
                    style: TextStyle(
                      color: DuolingoTheme.textSecondary,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(width: 8),
                  AnimatedBuilder(
                    animation: _typingAnimation,
                    builder: (context, child) {
                      return Row(
                        mainAxisSize: MainAxisSize.min,
                        children: List.generate(3, (index) {
                          final delay = index * 0.2;
                          final animationValue =
                              (_typingAnimation.value + delay) % 1.0;
                          return Container(
                            margin: const EdgeInsets.symmetric(horizontal: 1),
                            child: Transform.translate(
                              offset: Offset(
                                0,
                                -4 * (0.5 - (animationValue - 0.5).abs()) * 2,
                              ),
                              child: Container(
                                width: 6,
                                height: 6,
                                decoration: BoxDecoration(
                                  color: DuolingoTheme.primaryGreen,
                                  shape: BoxShape.circle,
                                ),
                              ),
                            ),
                          );
                        }),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء حقل إدخال الرسالة
  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: DuolingoTheme.cardShadow,
        border: Border(
          top: BorderSide(color: DuolingoTheme.borderLight, width: 1),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            // حقل الإدخال
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: DuolingoTheme.backgroundCard,
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(color: DuolingoTheme.borderLight),
                ),
                child: TextField(
                  controller: _messageController,
                  focusNode: _textFieldFocus,
                  decoration: InputDecoration(
                    hintText: 'اكتب رسالتك هنا...',
                    hintStyle: TextStyle(color: DuolingoTheme.textLight),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 12,
                    ),
                  ),
                  style: TextStyle(color: DuolingoTheme.textPrimary),
                  textInputAction: TextInputAction.send,
                  onSubmitted: (_) => _sendMessage(),
                  maxLines: null,
                  enabled: !_isTyping,
                ),
              ),
            ),

            const SizedBox(width: 12),

            // زر الإرسال
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              child: Material(
                color:
                    _isTyping
                        ? DuolingoTheme.borderMedium
                        : DuolingoTheme.primaryGreen,
                borderRadius: BorderRadius.circular(25),
                child: InkWell(
                  onTap: _isTyping ? null : _sendMessage,
                  borderRadius: BorderRadius.circular(25),
                  child: SizedBox(
                    width: 50,
                    height: 50,
                    child: Icon(Icons.send, color: Colors.white, size: 24),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر الرسالة
  Widget _buildMessageItem(ChatMessage message) {
    return Align(
          alignment:
              message.isUser ? Alignment.centerRight : Alignment.centerLeft,
          child: Container(
            margin: const EdgeInsets.only(bottom: 16),
            constraints: BoxConstraints(
              maxWidth: MediaQuery.of(context).size.width * 0.8,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                // أيقونة المساعد (للرسائل من الروبوت)
                if (!message.isUser) ...[
                  Container(
                    width: 32,
                    height: 32,
                    margin: const EdgeInsets.only(left: 8),
                    decoration: BoxDecoration(
                      color: DuolingoTheme.primaryGreen,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(Icons.smart_toy, color: Colors.white, size: 18),
                  ),
                ],

                // محتوى الرسالة
                Flexible(
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    decoration: BoxDecoration(
                      color:
                          message.isUser
                              ? DuolingoTheme.primaryBlue
                              : DuolingoTheme.backgroundCard,
                      borderRadius: BorderRadius.only(
                        topLeft: const Radius.circular(20),
                        topRight: const Radius.circular(20),
                        bottomLeft: Radius.circular(message.isUser ? 20 : 4),
                        bottomRight: Radius.circular(message.isUser ? 4 : 20),
                      ),
                      border:
                          message.isUser
                              ? null
                              : Border.all(color: DuolingoTheme.borderLight),
                      boxShadow:
                          message.isUser ? DuolingoTheme.buttonShadow : null,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // نص الرسالة
                        Text(
                          message.text,
                          style: TextStyle(
                            fontSize: 16,
                            color:
                                message.isUser
                                    ? Colors.white
                                    : DuolingoTheme.textPrimary,
                            height: 1.4,
                          ),
                        ),

                        const SizedBox(height: 6),

                        // توقيت الرسالة
                        Text(
                          _formatTimestamp(message.timestamp),
                          style: TextStyle(
                            fontSize: 11,
                            color:
                                message.isUser
                                    ? Colors.white.withValues(alpha: 0.7)
                                    : DuolingoTheme.textLight,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // أيقونة المستخدم (للرسائل من المستخدم)
                if (message.isUser) ...[
                  Container(
                    width: 32,
                    height: 32,
                    margin: const EdgeInsets.only(right: 8),
                    decoration: BoxDecoration(
                      color: DuolingoTheme.primaryOrange,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(Icons.person, color: Colors.white, size: 18),
                  ),
                ],
              ],
            ),
          ),
        )
        .animate()
        .fadeIn(duration: 300.ms)
        .slideY(begin: 0.3, end: 0, duration: 300.ms);
  }
}
