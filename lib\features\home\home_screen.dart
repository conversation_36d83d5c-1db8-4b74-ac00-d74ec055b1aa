import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart' hide AppState;
import '../../core/app_state.dart';
import '../../config/app_theme.dart';
import '../../widgets/enhanced_drawer.dart';
import '../../widgets/enhanced_bottom_navigation.dart';
import '../translation/voice/voice_translation_screen.dart';
import '../translation/voice/enhanced_voice_translation_screen.dart';
import '../translation/text/text_translation_screen.dart';
import '../translation/image/image_translation_screen.dart';
import '../translation/document/document_translation_screen.dart';
import '../translation/conversation/conversation_translation_screen.dart';
import '../translation/conversation/multi_speaker_conversation_screen.dart';
import '../translation/conversation/real_time_conversation_screen.dart';
import '../translation/kids_mode/kids_mode_screen.dart';
import '../translation/tourism_mode/tourism_mode_screen.dart';
import '../translation/tourism_mode/tourism_phrases_screen.dart';
import '../../screens/children/interactive_learning_screen.dart';
import '../chat/ai_chat_screen.dart';
import '../chat/gemini_chat_button.dart';
import '../settings/settings_screen.dart';
import '../subscription/subscription_screen.dart';

/// الشاشة الرئيسية للتطبيق
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;
  BannerAd? _bannerAd;
  bool _isAdLoaded = false;

  @override
  void initState() {
    super.initState();
    // تأخير تحميل الإعلان حتى يتم بناء الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadBannerAd();

      // إضافة رسالة تشخيصية للتأكد من أن الشاشة تم بناؤها بشكل صحيح
      debugPrint('HomeScreen initialized successfully');
    });
  }

  @override
  void dispose() {
    _bannerAd?.dispose();
    super.dispose();
  }

  /// تحميل إعلان البانر
  Future<void> _loadBannerAd() async {
    try {
      final appState = Provider.of<AppState>(context, listen: false);

      // التحقق من أن خدمة الإعلانات جاهزة
      if (appState.isLoading) {
        // إعادة المحاولة بعد فترة إذا كان التطبيق لا يزال في مرحلة التحميل
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) _loadBannerAd();
        });
        return;
      }

      final adsService = appState.adsService;
      final bannerAd = await adsService.loadBannerAd();

      if (bannerAd != null && mounted) {
        setState(() {
          _bannerAd = bannerAd;
          _isAdLoaded = true;
        });
      }
    } catch (e) {
      debugPrint('Error loading banner ad: $e');
      // لا نقوم بتعيين _isAdLoaded إلى true في حالة حدوث خطأ
    }
  }

  /// التنقل إلى شاشة الميزة
  void _navigateToFeature(String featureId) {
    // طباعة معرف الميزة للتشخيص
    debugPrint('Navigating to feature: $featureId');

    try {
      // تحديد الشاشة المناسبة
      Widget screen;

      switch (featureId) {
        case 'voice':
          screen = const VoiceTranslationScreen();
          break;
        case 'enhanced_voice':
          screen = const EnhancedVoiceTranslationScreen();
          break;
        case 'text':
          screen = const TextTranslationScreen();
          break;
        case 'image':
          screen = const ImageTranslationScreen();
          break;
        case 'document':
          screen = const DocumentTranslationScreen();
          break;
        case 'conversation':
          screen = const ConversationTranslationScreen();
          break;
        case 'real_time_conversation':
          screen = const RealTimeConversationScreen();
          break;
        case 'multi_speaker':
          screen = const MultiSpeakerConversationScreen();
          break;
        case 'kids_mode':
          screen = const KidsModeScreen();
          break;
        case 'interactive_learning':
          screen = const InteractiveLearningScreen();
          break;
        case 'tourism_mode':
          screen = const TourismModeScreen();
          break;
        case 'tourism_phrases':
          screen = const TourismPhrasesScreen();
          break;
        case 'ai_chat':
          screen = const AIChatScreen();
          break;
        default:
          // إظهار رسالة خطأ إذا كان معرف الميزة غير معروف
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ: معرف الميزة غير معروف: $featureId'),
              backgroundColor: Colors.red,
            ),
          );
          return;
      }

      // استخدام الانتقال العادي مباشرة لتجنب المشاكل
      debugPrint('Pushing route to: $featureId');

      // استخدام انتقال بسيط بدون تأثيرات معقدة
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => screen,
          settings: RouteSettings(name: featureId),
        ),
      );

      // إظهار رسالة نجاح للتشخيص
      debugPrint('Navigation successful to: $featureId');
    } catch (e) {
      // التقاط أي استثناءات وعرضها للتشخيص
      debugPrint('Error navigating to feature: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء الانتقال: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// التنقل إلى شاشة الإعدادات
  void _navigateToSettings() {
    // استخدام الانتقال العادي مباشرة لتجنب المشاكل
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (_) => const SettingsScreen()));
  }

  /// التنقل إلى شاشة الاشتراكات
  void _navigateToSubscriptions() {
    // استخدام الانتقال العادي مباشرة لتجنب المشاكل
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (_) => const SubscriptionScreen()));
  }

  /// تغيير الصفحة الحالية
  void _onTabChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AppState>(
      builder: (context, appState, child) {
        // عرض مؤشر التحميل إذا كان التطبيق في مرحلة التهيئة
        if (appState.isLoading) {
          return Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(color: AppTheme.primaryColor),
                  const SizedBox(height: 20),
                  const Text(
                    'جاري تحميل التطبيق...',
                    style: TextStyle(fontSize: 16),
                  ),
                ],
              ),
            ),
          );
        }

        return Scaffold(
          appBar: AppBar(
            title: const Text('المترجم الذكي AI'),
            centerTitle: true,
            // استخدام سمة التطبيق بدلاً من لون ثابت
            backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
            foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
            actions: [
              // زر الإعدادات
              IconButton(
                icon: const Icon(Icons.settings),
                onPressed: _navigateToSettings,
              ),
            ],
          ),
          drawer: _buildDrawer(appState),
          body: Column(
            children: [
              // المحتوى الرئيسي
              Expanded(child: _buildBody()),

              // إعلان البانر
              if (_isAdLoaded && !appState.isAdsRemoved && _bannerAd != null)
                Container(
                  width: _bannerAd!.size.width.toDouble(),
                  height: _bannerAd!.size.height.toDouble(),
                  alignment: Alignment.center,
                  child: AdWidget(ad: _bannerAd!),
                ),
            ],
          ),
          bottomNavigationBar: EnhancedBottomNavigation(
            currentIndex: _currentIndex,
            onTap: _onTabChanged,
            selectedItemColor: Theme.of(context).colorScheme.primary,
            unselectedItemColor:
                Theme.of(context).brightness == Brightness.dark
                    ? Colors.grey.shade400
                    : Colors.grey.shade600,
            backgroundColor:
                Theme.of(context).brightness == Brightness.dark
                    ? Theme.of(context).cardTheme.color
                    : Colors.white,
            borderRadius: 16,
            elevation: 8,
            height: 64,
            items: const [
              EnhancedBottomNavigationItem(icon: Icons.home, label: 'الرئيسية'),
              EnhancedBottomNavigationItem(
                icon: Icons.favorite,
                label: 'المفضلة',
              ),
              EnhancedBottomNavigationItem(icon: Icons.history, label: 'السجل'),
            ],
          ),
          floatingActionButton: const GeminiChatButton(),
        );
      },
    );
  }

  /// بناء القائمة الجانبية
  Widget _buildDrawer(AppState appState) {
    // إنشاء رأس القائمة
    final drawerHeader = DrawerHeader(
      decoration: const BoxDecoration(color: AppTheme.primaryColor),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // صورة المستخدم
          CircleAvatar(
            radius: 30,
            backgroundColor: Colors.white,
            child: Text(
              appState.currentUser?.displayName != null &&
                      appState.currentUser!.displayName!.isNotEmpty
                  ? appState.currentUser!.displayName!
                      .substring(0, 1)
                      .toUpperCase()
                  : 'G',
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
          ),

          const SizedBox(height: 10),

          // اسم المستخدم
          Text(
            appState.currentUser?.displayName ?? 'ضيف',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),

          // البريد الإلكتروني
          if (appState.currentUser?.email != null &&
              appState.currentUser!.email!.isNotEmpty)
            Text(
              appState.currentUser!.email!,
              style: const TextStyle(
                color: Color.fromRGBO(255, 255, 255, 0.8),
                fontSize: 14,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
        ],
      ),
    );

    // إنشاء عناصر القائمة
    final drawerItems = [
      EnhancedDrawerItem(icon: Icons.home, title: 'الرئيسية', onTap: () {}),

      EnhancedDrawerItem(
        icon: Icons.settings,
        title: 'الإعدادات',
        onTap: _navigateToSettings,
      ),

      EnhancedDrawerItem(
        icon: Icons.workspace_premium,
        title: 'الاشتراكات',
        subtitle: appState.isAdsRemoved ? 'مشترك' : 'غير مشترك',
        onTap: _navigateToSubscriptions,
        trailing:
            appState.isAdsRemoved
                ? const Icon(Icons.check_circle, color: Colors.green, size: 20)
                : null,
      ),

      EnhancedDrawerItem.divider(),

      EnhancedDrawerItem(
        icon: Icons.logout,
        title: 'تسجيل الخروج',
        onTap: () async {
          await appState.signOut();
        },
        iconColor: Colors.red,
      ),
    ];

    return EnhancedDrawer(
      header: drawerHeader,
      items: drawerItems,
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      itemHeight: 56,
      itemSpacing: 4,
    );
  }

  /// بناء محتوى الصفحة الرئيسية مع تأثيرات انتقالية سلسة
  Widget _buildBody() {
    // قائمة الصفحات
    final List<Widget> pages = [
      _buildHomeTab(),
      _buildFavoritesTab(),
      _buildHistoryTab(),
    ];

    // استخدام IndexedStack مع تأثيرات انتقالية سلسة
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 400),
      transitionBuilder: (Widget child, Animation<double> animation) {
        // تأثير التلاشي مع حركة خفيفة
        return FadeTransition(
          opacity: animation,
          child: SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0.05, 0),
              end: Offset.zero,
            ).animate(
              CurvedAnimation(parent: animation, curve: Curves.easeOutQuint),
            ),
            child: child,
          ),
        );
      },
      child: KeyedSubtree(
        key: ValueKey<int>(_currentIndex),
        child: pages[_currentIndex],
      ),
    );
  }

  /// بناء علامة التبويب الرئيسية
  Widget _buildHomeTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان الصفحة
          Text(
            'مرحباً بك في المترجم الذكي AI',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 8),

          // وصف الصفحة
          Text(
            'اختر إحدى ميزات الترجمة التالية',
            style: Theme.of(context).textTheme.bodyLarge,
          ),

          const SizedBox(height: 24),

          // قائمة الميزات المبسطة
          Column(
            children: [
              // الصف الأول: ترجمة صوتية وترجمة صوتية محسنة
              Row(
                children: [
                  Expanded(
                    child: _buildSimpleFeatureCard(
                      id: 'voice',
                      title: 'ترجمة صوتية',
                      icon: Icons.mic,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildSimpleFeatureCard(
                      id: 'enhanced_voice',
                      title: 'ترجمة صوتية محسنة',
                      icon: Icons.record_voice_over,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // الصف الثاني: ترجمة نصوص ومحادثة ذكية
              Row(
                children: [
                  Expanded(
                    child: _buildSimpleFeatureCard(
                      id: 'text',
                      title: 'ترجمة نصوص',
                      icon: Icons.text_fields,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildSimpleFeatureCard(
                      id: 'ai_chat',
                      title: 'محادثة ذكية',
                      icon: Icons.smart_toy,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // الصف الثالث: ترجمة صور وترجمة مستندات
              Row(
                children: [
                  Expanded(
                    child: _buildSimpleFeatureCard(
                      id: 'image',
                      title: 'ترجمة صور',
                      icon: Icons.camera_alt,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildSimpleFeatureCard(
                      id: 'document',
                      title: 'ترجمة مستندات',
                      icon: Icons.description,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // الصف الرابع: ترجمة محادثات وترجمة متعددة المتحدثين
              Row(
                children: [
                  Expanded(
                    child: _buildSimpleFeatureCard(
                      id: 'conversation',
                      title: 'ترجمة محادثات',
                      icon: Icons.forum,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildSimpleFeatureCard(
                      id: 'multi_speaker',
                      title: 'متعددة المتحدثين',
                      icon: Icons.groups,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // الصف الخامس: وضع الأطفال ووضع السياحة
              Row(
                children: [
                  Expanded(
                    child: _buildSimpleFeatureCard(
                      id: 'kids_mode',
                      title: 'وضع الأطفال',
                      icon: Icons.child_care,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildSimpleFeatureCard(
                      id: 'interactive_learning',
                      title: 'التعلم التفاعلي',
                      icon: Icons.school,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // الصف السادس: عبارات سياحية
              Row(
                children: [
                  Expanded(
                    child: _buildSimpleFeatureCard(
                      id: 'tourism_phrases',
                      title: 'عبارات سياحية',
                      icon: Icons.menu_book,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildSimpleFeatureCard(
                      id: 'tourism_mode',
                      title: 'وضع السياحة',
                      icon: Icons.flight,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء علامة التبويب المفضلة
  Widget _buildFavoritesTab() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isDark = theme.brightness == Brightness.dark;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // أيقونة فارغة
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color:
                  isDark
                      ? colorScheme.surface.withAlpha(100)
                      : colorScheme.surfaceContainerLow.withAlpha(180),
              shape: BoxShape.circle,
              border: Border.all(
                color:
                    isDark
                        ? colorScheme.outline.withAlpha(40)
                        : colorScheme.outline.withAlpha(30),
                width: 1,
              ),
            ),
            child: Icon(
              Icons.favorite_border,
              size: 48,
              color:
                  isDark
                      ? colorScheme.primary.withAlpha(180)
                      : colorScheme.primary.withAlpha(200),
            ),
          ),
          const SizedBox(height: 24),
          // نص توضيحي
          Text(
            'لا توجد عناصر مفضلة حتى الآن',
            style: TextStyle(
              fontSize: 18,
              color:
                  isDark
                      ? colorScheme.onSurface.withAlpha(180)
                      : colorScheme.onSurface.withAlpha(180),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 12),
          // نص إرشادي
          Text(
            'ستظهر هنا العناصر التي تضيفها إلى المفضلة',
            style: TextStyle(
              fontSize: 14,
              color:
                  isDark
                      ? colorScheme.onSurfaceVariant.withAlpha(150)
                      : colorScheme.onSurfaceVariant.withAlpha(150),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء علامة التبويب السجل
  Widget _buildHistoryTab() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isDark = theme.brightness == Brightness.dark;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // أيقونة فارغة
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color:
                  isDark
                      ? colorScheme.surface.withAlpha(100)
                      : colorScheme.surfaceContainerLow.withAlpha(180),
              shape: BoxShape.circle,
              border: Border.all(
                color:
                    isDark
                        ? colorScheme.outline.withAlpha(40)
                        : colorScheme.outline.withAlpha(30),
                width: 1,
              ),
            ),
            child: Icon(
              Icons.history,
              size: 48,
              color:
                  isDark
                      ? colorScheme.secondary.withAlpha(180)
                      : colorScheme.secondary.withAlpha(200),
            ),
          ),
          const SizedBox(height: 24),
          // نص توضيحي
          Text(
            'لا يوجد سجل حتى الآن',
            style: TextStyle(
              fontSize: 18,
              color:
                  isDark
                      ? colorScheme.onSurface.withAlpha(180)
                      : colorScheme.onSurface.withAlpha(180),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 12),
          // نص إرشادي
          Text(
            'سيظهر هنا سجل الترجمات السابقة',
            style: TextStyle(
              fontSize: 14,
              color:
                  isDark
                      ? colorScheme.onSurfaceVariant.withAlpha(150)
                      : colorScheme.onSurfaceVariant.withAlpha(150),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة الميزة البسيطة بتصميم أبسط لضمان الاستجابة
  Widget _buildSimpleFeatureCard({
    required String id,
    required String title,
    required IconData icon,
  }) {
    // استخدام ElevatedButton بدلاً من GestureDetector أو InkWell
    return ElevatedButton(
      onPressed: () {
        debugPrint('Button tapped: $id');
        // تنفيذ الانتقال مباشرة
        _navigateToFeature(id);
      },
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
        backgroundColor: Theme.of(context).cardColor,
        foregroundColor: Theme.of(context).colorScheme.primary,
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // أيقونة الميزة
          Icon(icon, size: 36, color: Theme.of(context).colorScheme.primary),
          const SizedBox(height: 12),
          // عنوان الميزة
          Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).textTheme.titleLarge?.color,
            ),
          ),
        ],
      ),
    );
  }
}
