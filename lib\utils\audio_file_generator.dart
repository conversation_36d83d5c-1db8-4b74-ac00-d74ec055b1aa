import 'dart:math';
import 'dart:typed_data';

/// مولد ملفات الصوت الاحترافية
class AudioFileGenerator {
  static const int sampleRate = 44100;
  static const double pi = 3.14159265359;

  /// توليد ملف صوتي للإجابة الصحيحة
  static Uint8List generateCorrectSoundFile() {
    final frequencies = [523.25, 659.25, 783.99, 1046.50]; // C5, E5, G5, C6
    final durations = [0.15, 0.15, 0.15, 0.3]; // بالثواني

    return _generateMelodyFile(frequencies, durations, volume: 0.7);
  }

  /// توليد ملف صوتي للإجابة الخاطئة
  static Uint8List generateIncorrectSoundFile() {
    final frequencies = [392.00, 329.63, 261.63]; // G4, E4, C4
    final durations = [0.2, 0.2, 0.4];

    return _generateMelodyFile(frequencies, durations, volume: 0.6);
  }

  /// توليد ملف صوتي لإكمال المستوى
  static Uint8List generateLevelCompleteSoundFile() {
    final frequencies = [
      523.25,
      659.25,
      783.99,
      1046.50,
      783.99,
      1046.50,
      1174.66,
      1318.51,
    ];
    final durations = [0.15, 0.15, 0.15, 0.15, 0.15, 0.3, 0.15, 0.4];

    return _generateMelodyFile(frequencies, durations, volume: 0.8);
  }

  /// توليد ملف صوتي للإنجاز
  static Uint8List generateAchievementSoundFile() {
    final frequencies = [800.0, 1000.0, 1200.0, 1400.0, 1600.0, 1318.51];
    final durations = [0.1, 0.1, 0.1, 0.1, 0.1, 0.3];

    return _generateMelodyFile(frequencies, durations, volume: 0.6);
  }

  /// توليد ملف صوتي لفقدان القلب
  static Uint8List generateHeartLostSoundFile() {
    final frequencies = [440.00, 369.99, 293.66, 220.00]; // A4, F#4, D4, A3
    final durations = [0.2, 0.2, 0.2, 0.3];

    return _generateMelodyFile(frequencies, durations, volume: 0.5);
  }

  /// توليد ملف صوتي للنقر على الزر
  static Uint8List generateButtonClickSoundFile() {
    return _generateToneFile(800, 0.05, volume: 0.4);
  }

  /// توليد ملف صوتي للسلسلة المتتالية
  static Uint8List generateStreakSoundFile() {
    final frequencies = [600.0, 750.0, 900.0];
    final durations = [0.12, 0.12, 0.12];

    return _generateMelodyFile(frequencies, durations, volume: 0.6);
  }

  /// توليد ملف صوتي للإشعار
  static Uint8List generateNotificationSoundFile() {
    final frequencies = [659.25, 783.99]; // E5, G5
    final durations = [0.15, 0.2];

    return _generateMelodyFile(frequencies, durations, volume: 0.5);
  }

  /// توليد موسيقى خلفية هادئة
  static Uint8List generateBackgroundMusicFile() {
    final melody = [
      523.25,
      587.33,
      659.25,
      698.46,
      783.99,
      698.46,
      659.25,
      587.33,
    ]; // C5, D5, E5, F5, G5, F5, E5, D5

    final durations = List.filled(melody.length, 0.4);

    return _generateMelodyFile(melody, durations, volume: 0.2);
  }

  /// توليد لحن من عدة نغمات
  static Uint8List _generateMelodyFile(
    List<double> frequencies,
    List<double> durations, {
    double volume = 0.5,
  }) {
    final allSamples = <double>[];

    for (int i = 0; i < frequencies.length; i++) {
      final frequency = frequencies[i];
      final duration = durations[i];
      final samples = _generateToneSamples(frequency, duration, volume: volume);

      allSamples.addAll(samples);

      // إضافة فجوة صغيرة بين النغمات
      if (i < frequencies.length - 1) {
        final gapSamples = (sampleRate * 0.05).round(); // 50ms gap
        allSamples.addAll(List.filled(gapSamples, 0.0));
      }
    }

    return _convertToWavFile(allSamples);
  }

  /// توليد نغمة واحدة
  static Uint8List _generateToneFile(
    double frequency,
    double duration, {
    double volume = 0.5,
  }) {
    final samples = _generateToneSamples(frequency, duration, volume: volume);
    return _convertToWavFile(samples);
  }

  /// توليد عينات صوتية لنغمة واحدة
  static List<double> _generateToneSamples(
    double frequency,
    double duration, {
    double volume = 0.5,
  }) {
    final numSamples = (sampleRate * duration).round();
    final samples = <double>[];

    for (int i = 0; i < numSamples; i++) {
      final t = i / sampleRate;

      // تأثير fade in/out لتجنب الطقطقة
      final fadeIn = i < numSamples * 0.1 ? i / (numSamples * 0.1) : 1.0;
      final fadeOut =
          i > numSamples * 0.9 ? (numSamples - i) / (numSamples * 0.1) : 1.0;
      final fade = fadeIn * fadeOut;

      // موجة جيبية مع هارمونيات لصوت أكثر ثراءً
      final fundamental = sin(2 * pi * frequency * t);
      final harmonic2 = sin(2 * pi * frequency * 2 * t) * 0.3;
      final harmonic3 = sin(2 * pi * frequency * 3 * t) * 0.1;

      final sample = (fundamental + harmonic2 + harmonic3) * volume * fade;
      samples.add(sample);
    }

    return samples;
  }

  /// تحويل العينات إلى ملف WAV
  static Uint8List _convertToWavFile(List<double> samples) {
    final numSamples = samples.length;
    final dataSize = numSamples * 2; // 16-bit samples
    final fileSize = 44 + dataSize; // WAV header is 44 bytes

    final buffer = ByteData(fileSize);

    // WAV Header
    // RIFF chunk
    buffer.setUint32(0, 0x52494646, Endian.big); // "RIFF"
    buffer.setUint32(4, fileSize - 8, Endian.little);
    buffer.setUint32(8, 0x57415645, Endian.big); // "WAVE"

    // fmt chunk
    buffer.setUint32(12, 0x666d7420, Endian.big); // "fmt "
    buffer.setUint32(16, 16, Endian.little); // chunk size
    buffer.setUint16(20, 1, Endian.little); // audio format (PCM)
    buffer.setUint16(22, 1, Endian.little); // num channels (mono)
    buffer.setUint32(24, sampleRate, Endian.little); // sample rate
    buffer.setUint32(28, sampleRate * 2, Endian.little); // byte rate
    buffer.setUint16(32, 2, Endian.little); // block align
    buffer.setUint16(34, 16, Endian.little); // bits per sample

    // data chunk
    buffer.setUint32(36, 0x64617461, Endian.big); // "data"
    buffer.setUint32(40, dataSize, Endian.little);

    // Audio data
    for (int i = 0; i < numSamples; i++) {
      final sample = (samples[i] * 32767).round().clamp(-32768, 32767);
      buffer.setInt16(44 + i * 2, sample, Endian.little);
    }

    return buffer.buffer.asUint8List();
  }

  /// حفظ الملفات الصوتية
  static Map<String, Uint8List> generateAllSoundFiles() {
    return {
      'correct.wav': generateCorrectSoundFile(),
      'incorrect.wav': generateIncorrectSoundFile(),
      'level_complete.wav': generateLevelCompleteSoundFile(),
      'achievement.wav': generateAchievementSoundFile(),
      'heart_lost.wav': generateHeartLostSoundFile(),
      'button_click.wav': generateButtonClickSoundFile(),
      'streak.wav': generateStreakSoundFile(),
      'notification.wav': generateNotificationSoundFile(),
      'background_music.wav': generateBackgroundMusicFile(),
    };
  }
}

/// خدمة توليد أصوات النطق للغات
class LanguagePronunciationGenerator {
  /// توليد أصوات النطق الأساسية
  static Map<String, Map<String, Uint8List>> generateBasicPronunciations() {
    final pronunciations = <String, Map<String, Uint8List>>{};

    // كلمات أساسية مع ترددات مختلفة لمحاكاة اللغات المختلفة
    final words = {
      'hello': {
        'ar': [440.0, 523.25, 659.25], // مرحبا
        'en': [523.25, 659.25, 783.99], // Hello
        'fr': [587.33, 698.46, 830.61], // Bonjour
        'de': [493.88, 587.33, 698.46], // Hallo
        'es': [523.25, 622.25, 739.99], // Hola
        'it': [554.37, 659.25, 783.99], // Ciao
        'ja': [466.16, 554.37, 659.25, 783.99], // こんにちは
        'zh': [523.25, 622.25, 739.99, 880.00], // 你好
        'tr': [493.88, 587.33, 698.46], // Merhaba
        'ru': [440.00, 523.25, 622.25, 739.99], // Привет
      },
      'goodbye': {
        'ar': [783.99, 659.25, 523.25], // وداعا
        'en': [659.25, 783.99, 932.33], // Goodbye
        'fr': [698.46, 830.61, 987.77], // Au revoir
        'de': [587.33, 698.46, 830.61, 987.77], // Auf Wiedersehen
        'es': [622.25, 739.99, 880.00], // Adiós
        'it': [659.25, 783.99, 932.33, 1108.73], // Arrivederci
        'ja': [554.37, 659.25, 783.99, 932.33], // さようなら
        'zh': [622.25, 739.99, 880.00], // 再见
        'tr': [587.33, 698.46, 830.61], // Hoşça kal
        'ru': [523.25, 622.25, 739.99, 880.00], // До свидания
      },
    };

    for (final wordEntry in words.entries) {
      final word = wordEntry.key;
      pronunciations[word] = {};

      for (final langEntry in wordEntry.value.entries) {
        final lang = langEntry.key;
        final frequencies = langEntry.value;

        // توليد نطق الكلمة
        final durations = List.filled(frequencies.length, 0.3);
        final audioData = AudioFileGenerator._generateMelodyFile(
          frequencies,
          durations,
          volume: 0.6,
        );

        pronunciations[word]![lang] = audioData;
      }
    }

    return pronunciations;
  }
}

/// خدمة إنشاء الأصوات البيئية
class AmbientSoundGenerator {
  /// توليد أصوات طبيعية مهدئة
  static Uint8List generateNatureSounds() {
    final samples = <double>[];
    final duration = 30.0; // 30 ثانية
    final numSamples = (AudioFileGenerator.sampleRate * duration).round();

    final random = Random();

    for (int i = 0; i < numSamples; i++) {
      final t = i / AudioFileGenerator.sampleRate;

      // صوت المطر الخفيف
      final rain = (random.nextDouble() - 0.5) * 0.1;

      // صوت الرياح
      final wind = sin(2 * AudioFileGenerator.pi * 0.5 * t) * 0.05;

      // أصوات طيور بعيدة
      final birds =
          sin(2 * AudioFileGenerator.pi * 800 * t) *
          sin(2 * AudioFileGenerator.pi * 0.1 * t) *
          0.02;

      final sample = rain + wind + birds;
      samples.add(sample);
    }

    return AudioFileGenerator._convertToWavFile(samples);
  }

  /// توليد موسيقى تركيز
  static Uint8List generateFocusMusic() {
    final samples = <double>[];
    final duration = 60.0; // دقيقة واحدة
    final numSamples = (AudioFileGenerator.sampleRate * duration).round();

    // نغمات هادئة ومتكررة
    final baseFrequencies = [261.63, 293.66, 329.63, 349.23]; // C4, D4, E4, F4

    for (int i = 0; i < numSamples; i++) {
      final t = i / AudioFileGenerator.sampleRate;
      var sample = 0.0;

      for (final freq in baseFrequencies) {
        // نغمات متداخلة بأطوار مختلفة
        sample += sin(2 * AudioFileGenerator.pi * freq * t) * 0.1;
        sample += sin(2 * AudioFileGenerator.pi * freq * 1.5 * t) * 0.05;
      }

      // تأثير fade in/out للحلقة السلسة
      final fadeIn = i < numSamples * 0.1 ? i / (numSamples * 0.1) : 1.0;
      final fadeOut =
          i > numSamples * 0.9 ? (numSamples - i) / (numSamples * 0.1) : 1.0;
      final fade = fadeIn * fadeOut;

      samples.add(sample * fade * 0.3);
    }

    return AudioFileGenerator._convertToWavFile(samples);
  }
}
